# SUSE AI Runtime (AIR)

A comprehensive Go application that converts OpenAPI v3 specifications into MCP (Model Context Protocol) tools and provides a complete ecosystem for managing and executing them.

## 🚀 Features

- **🔄 OpenAPI to MCP Conversion**: Automatically convert REST APIs to MCP tools
- **🌐 MCP Server**: Full MCP protocol-compliant server with authentication
- **👥 Multi-Tenant Profiles**: Organize tools by profiles with isolated access
- **🔐 Authentication & Authorization**: JWT-based security with role-based access control
- **🔗 Connection Management**: Secure storage and management of API connections
- **🛡️ Multiple Auth Types**: Support for Basic, Bearer, and Custom Header authentication
- **🔒 Encrypted Secrets**: AES-GCM encryption for sensitive authentication data
- **💻 Interactive TUI**: Beautiful terminal user interface for all management operations
- **⚡ CLI Tools**: Complete command-line interface for all operations
- **🧪 Testing**: Comprehensive test suite with integration and unit tests
- **📊 MCP Inspector Compatible**: Works with official MCP testing tools

## 🏗️ Architecture

SUSE AIR bridges the gap between traditional REST APIs and the Model Context Protocol by:

1. **Converting** OpenAPI specifications into MCP tool definitions
2. **Storing** conversion mappings in PostgreSQL for request reconstruction
3. **Managing** secure connections to backend REST services with encrypted authentication
4. **Serving** MCP-compliant endpoints with proper authentication and dynamic execution
5. **Managing** multi-tenant access through profiles and roles
6. **Providing** intuitive interfaces for administration and connection management

### System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   OpenAPI       │    │   SUSE AIR       │    │   MCP Client    │
│   Specification │───▶│   (MCP Server)   │◀───│   (AI System)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   PostgreSQL     │    │   Backend       │
                       │   Database       │◀───│   REST APIs     │
                       │                  │    │                 │
                       │ • MCP Tools      │    │ • Authenticated │
                       │ • Tool Mappings  │    │ • Dynamic URLs  │
                       │ • Profiles       │    │ • Multi-tenant  │
                       │ • Users & Roles  │    └─────────────────┘
                       │ • Connections    │
                       │ • Schemas        │
                       │ • Encrypted Auth │
                       └──────────────────┘
```

### Key Components

- **🔄 Schema Converter**: Transforms OpenAPI specs into MCP tool definitions
- **🔗 Connection Manager**: Securely stores and manages API authentication credentials
- **🛡️ Encryption Engine**: AES-GCM encryption for sensitive authentication data
- **🌐 MCP Server**: Protocol-compliant server with dynamic tool execution
- **👥 Profile System**: Multi-tenant organization with role-based access control
- **💻 TUI/CLI**: Interactive interfaces for administration and management

## 🛠️ Prerequisites

- **Go 1.22+**: For building and running the application
- **PostgreSQL 13+**: For data storage and management
- **Docker** (optional): For easy database setup and testing

## 📦 Quick Start

### 1. Clone and Build

```bash
git clone https://github.com/ravan/suse-air.git
cd suse-air

# Install dependencies and build
make deps
make build
```

### 2. Database Setup

**Option A: Using Docker (Recommended)**
```bash
# Start PostgreSQL container
make dev-db

# Run migrations
make migrate-dev
```

**Option B: Existing PostgreSQL**
```bash
# Create database
createdb sairdev

# Run migrations
cd sql/migrations
goose postgres "postgres://user:pass@localhost:5432/sairdev?sslmode=disable" up
```

### 3. Start Using SUSE AIR

**Interactive Terminal Interface:**
```bash
./air tui --db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
```

**MCP Server:**
```bash
./air serve --port 8080 --db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
```

## 📖 Usage Guide

### 🎯 Core Workflow

1. **Convert OpenAPI to MCP Tools** - Transform REST API specs into MCP tool definitions
2. **Create Connections** - Set up secure connections to backend REST services
3. **Associate Connections with Schemas** - Link connections to converted OpenAPI schemas
4. **Create Profiles** - Organize tools into logical groups
5. **Associate Tools** with profiles and set access permissions
6. **Start MCP Server** - Serve tools with dynamic authentication and execution
7. **Test with MCP Inspector** or integrate with AI systems

### 💻 Terminal User Interface (TUI)

The TUI provides an intuitive interface for all management operations:

```bash
./air tui --db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
```

**Features:**
- 📋 **Profile Management**: Create, update, delete profiles
- 🔗 **Tool Association**: Link tools to profiles with access control
- 👤 **User Management**: Create users and assign roles
- 🔌 **Connection Management**: Create, manage, and test API connections
- 🔐 **Schema Associations**: Link connections with OpenAPI schemas
- ⌨️ **Keyboard Navigation**: Arrow keys, Tab, Enter for navigation
- ✅ **Real-time Feedback**: Success/error messages for all operations
- 🛡️ **Secure Forms**: Password-masked input for sensitive data

### 🌐 MCP Server

Start the MCP protocol server:

```bash
./air serve --port 8080 --db "postgres://..."
```

**Endpoints:**
- `POST /mcp/{profile}` - MCP protocol endpoint for each profile
- `GET /auth` - Token generation for authentication
- `GET /health` - Health check endpoint

**Testing with MCP Inspector:**
```bash
# Install and run MCP Inspector
npx @modelcontextprotocol/inspector http://localhost:8080/mcp/my-profile
```

### ⚙️ Command Line Interface

#### OpenAPI Conversion
```bash
# Convert OpenAPI spec to MCP tools
./air convert -f openapi.json --db "postgres://..."
```

#### Profile Management
```bash
# Create a new profile
./air profile create --name "billing-api" --description "Billing API tools" --path-segment "billing" --db "postgres://..."

# List all profiles
./air profile list --db "postgres://..."

# Update a profile
./air profile update --name "billing-api" --description "Updated description" --db "postgres://..."

# Delete a profile
./air profile delete --name "billing-api" --db "postgres://..."
```

#### Tool Management
```bash
# List tools for a profile
./air tool list --profile "billing-api" --db "postgres://..."

# Associate a tool with a profile
./air tool associate --profile "billing-api" --tool "CreateInvoice" --acl "EXECUTE" --db "postgres://..."

# Disassociate a tool from a profile
./air tool disassociate --profile "billing-api" --tool "CreateInvoice" --db "postgres://..."
```

#### User Management
```bash
# Create a user
./air user create --username "alice" --password "secure123" --db "postgres://..."

# List users
./air user list --db "postgres://..."

# Assign role to user
./air user assign-role --username "alice" --role "admin" --db "postgres://..."
```

#### Connection Management
```bash
# Create a connection with Bearer authentication
./air connection create \
  --name "my-api" \
  --description "My REST API" \
  --base-url "https://api.example.com" \
  --auth-type "bearer" \
  --secret "your-api-token" \
  --db "postgres://..."

# Create a connection with Basic authentication
./air connection create \
  --name "basic-api" \
  --base-url "https://api.basic.com" \
  --auth-type "basic" \
  --username "user" \
  --secret "password" \
  --db "postgres://..."

# Create a connection with Custom Header authentication
./air connection create \
  --name "custom-api" \
  --base-url "https://api.custom.com" \
  --auth-type "custom_header" \
  --header-name "X-API-Key" \
  --secret "api-key-value" \
  --db "postgres://..."

# List all connections
./air connection list --db "postgres://..."

# Get connection details
./air connection get --name "my-api" --db "postgres://..."

# Delete a connection
./air connection delete --name "my-api" --db "postgres://..."
```

#### Schema-Connection Association
```bash
# Associate a connection with a schema (set as default)
./air schema associate-connection \
  --schema "my-openapi-schema" \
  --connection "my-api" \
  --default \
  --db "postgres://..."

# Disassociate a connection from a schema
./air schema disassociate-connection \
  --schema "my-openapi-schema" \
  --connection "my-api" \
  --db "postgres://..."

# List connections for a schema
./air schema list-connections \
  --schema "my-openapi-schema" \
  --db "postgres://..."
```

#### Tool Execution with Connections
```bash
# Execute a tool using its associated connection for authentication
./air execute-with-connection \
  --tool "CreateUser" \
  --args '{"name": "John", "email": "<EMAIL>"}' \
  --db "postgres://..."
```

## 🔐 Authentication & Security

### JWT Authentication
SUSE AIR uses JWT tokens with audience validation for secure access:

```bash
# Request a token (in a real setup, this would be through OAuth2 flow)
curl -X GET "http://localhost:8080/auth"

# Use token in MCP requests
curl -X POST "http://localhost:8080/mcp/my-profile" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
```

### Multi-Tenant Security
- **Profile Isolation**: Each profile has its own MCP endpoint
- **Audience Validation**: Tokens are scoped to specific profiles
- **Access Control Lists**: Fine-grained permissions per tool
- **Role-Based Access**: Users can have different roles with varying permissions

### Connection Security
- **AES-GCM Encryption**: All sensitive authentication data encrypted at rest
- **Environment-based Keys**: Encryption keys managed through environment variables
- **Multiple Auth Types**: Support for Basic, Bearer, and Custom Header authentication
- **Secure Storage**: Passwords, tokens, and API keys never stored in plaintext
- **Connection Isolation**: Each connection is isolated and scoped to specific schemas
- **Dynamic Authentication**: Runtime authentication using stored, encrypted credentials

## 🧪 Testing

### Running Tests
```bash
# All tests
make test

# Unit tests only (no database required)
make test-unit

# Integration tests (requires PostgreSQL)
make test-integration

# Test coverage report
make test-coverage
```

### Test Categories
- **Unit Tests**: TUI models, authentication, schema conversion
- **Integration Tests**: End-to-end CLI and database operations
- **Protocol Tests**: MCP compliance and tool execution
- **Security Tests**: JWT validation and access control

## 🔧 Development

### Project Structure
```
suse-air/
├── cmd/air/              # Main application entry point
├── internal/
│   ├── cmd/              # CLI command implementations
│   ├── server/           # MCP server and authentication
│   ├── tui/              # Terminal user interface
│   ├── schema/           # OpenAPI to MCP conversion
│   ├── db/               # Database layer (generated)
│   └── models/           # Data structures
├── sql/
│   ├── migrations/       # Database migrations
│   └── query/            # SQL queries for code generation
└── tests/                # Test suites
```

### Adding New Features

1. **Database Changes**: Add migrations in `sql/migrations/`
2. **New Queries**: Add SQL in `sql/query/` and regenerate with `sqlc`
3. **CLI Commands**: Add to `internal/cmd/commands.go`
4. **TUI Features**: Extend models in `internal/tui/`
5. **Tests**: Add comprehensive tests for new functionality

### Code Generation
```bash
# Regenerate database code after SQL changes
cd sql && sqlc generate

# Install development dependencies
make deps
```

## ⚙️ Configuration

### Environment Variables

SUSE AIR supports the following environment variables:

```bash
# Database connection
export DATABASE_URL="postgres://user:pass@localhost:5432/sairdev?sslmode=disable"

# Connection encryption (REQUIRED for production)
export SAIR_ENCRYPTION_KEY="your-32-character-encryption-key-here"

# Server configuration
export PORT="8080"
export JWT_SECRET="your-jwt-secret-key"

# Development mode
export DEBUG="true"
```

**⚠️ Security Note**: Always set `SAIR_ENCRYPTION_KEY` in production environments. The system will use a default key for development but will warn you that it's not secure.

## 📊 Monitoring & Observability

### Health Checks
```bash
# Check server health
curl http://localhost:8080/health
```

### Logging
- **Structured Logging**: JSON-formatted logs for production
- **Debug Mode**: Verbose logging for development
- **Request Tracing**: Track MCP protocol requests and responses

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Add** comprehensive tests for your changes
4. **Commit** your changes (`git commit -m 'Add amazing feature'`)
5. **Push** to the branch (`git push origin feature/amazing-feature`)
6. **Open** a Pull Request

### Development Guidelines
- Write tests for all new functionality
- Follow Go best practices and conventions
- Update documentation for user-facing changes
- Ensure all tests pass before submitting PR

## ✅ Complete Feature Set

SUSE AIR provides a comprehensive solution for bridging REST APIs with the Model Context Protocol:

### 🔄 Core Conversion Engine
- ✅ OpenAPI 3.x specification parsing and validation
- ✅ Automatic MCP tool definition generation
- ✅ Parameter mapping and request reconstruction
- ✅ Support for complex nested objects and arrays
- ✅ Path parameter and query parameter handling

### 🔗 Connection Management
- ✅ Secure connection storage with AES-GCM encryption
- ✅ Multiple authentication types (None, Basic, Bearer, Custom Header)
- ✅ Dynamic base URL configuration per connection
- ✅ Schema-connection association management
- ✅ Default connection selection for schemas

### 🛡️ Security & Authentication
- ✅ JWT-based authentication with audience validation
- ✅ Role-based access control (RBAC)
- ✅ Multi-tenant profile isolation
- ✅ Encrypted storage of sensitive credentials
- ✅ Environment-based encryption key management

### 👥 User & Profile Management
- ✅ Multi-tenant profile system
- ✅ User creation and management
- ✅ Role assignment and permissions
- ✅ Tool-profile associations
- ✅ Access control lists per profile

### 💻 User Interfaces
- ✅ Interactive Terminal User Interface (TUI)
- ✅ Comprehensive Command Line Interface (CLI)
- ✅ Real-time feedback and validation
- ✅ Keyboard navigation and shortcuts
- ✅ Form-based input with validation

### 🌐 MCP Protocol Compliance
- ✅ Full MCP 1.0 protocol implementation
- ✅ Dynamic tool discovery and execution
- ✅ Proper JSON-RPC 2.0 message handling
- ✅ Error handling and status reporting
- ✅ Compatible with MCP Inspector and AI systems

### 🧪 Testing & Quality
- ✅ Comprehensive unit test suite
- ✅ Integration tests with real database
- ✅ CLI command testing
- ✅ TUI interaction testing
- ✅ End-to-end workflow validation

### 🚀 Production Ready
- ✅ Database migrations with rollback support
- ✅ Health check endpoints
- ✅ Structured logging
- ✅ Docker support for development
- ✅ Environment-based configuration

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: See [PLANNING.md](PLANNING.md) for detailed architecture
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions for questions and ideas

## 🙏 Acknowledgments

- **Model Context Protocol**: Built on the MCP specification
- **Bubble Tea**: Powers the beautiful terminal interface
- **sqlc**: Enables type-safe database operations
- **Goose**: Handles database migrations
- **PostgreSQL**: Reliable data storage
