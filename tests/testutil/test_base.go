package testutil

import (
	"context"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/util"
)

// TestData contains common test data factories
type TestData struct {
	queries *db.Queries
}

// NewTestData creates a new test data factory
func NewTestData(queries *db.Queries) *TestData {
	return &TestData{queries: queries}
}

// CreateTestProfile creates a test profile
func (td *TestData) CreateTestProfile(t *testing.T, name, pathSegment string) db.Profile {
	t.Helper()
	
	profile, err := td.queries.CreateProfile(context.Background(), db.CreateProfileParams{
		Name:        name,
		Description: pgtype.Text{String: "Test profile for " + name, Valid: true},
		PathSegment: pathSegment,
	})
	require.NoError(t, err)
	return profile
}

// CreateTestConnection creates a test connection
func (td *TestData) CreateTestConnection(t *testing.T, name, baseURL string) db.Connection {
	t.Helper()
	
	// Create encrypted auth token
	encKey := util.GetEncryptionKey()
	encryptedToken, err := encKey.Encrypt("test-token")
	require.NoError(t, err)
	
	conn, err := td.queries.CreateConnection(context.Background(), db.CreateConnectionParams{
		Name:               name,
		Description:        pgtype.Text{String: "Test connection for " + name, Valid: true},
		BaseUrl:            baseURL,
		AuthType:           "bearer",
		AuthTokenEncrypted: encryptedToken,
	})
	require.NoError(t, err)
	return conn
}

// CreateTestUser creates a test user
func (td *TestData) CreateTestUser(t *testing.T, username, password string) db.User {
	t.Helper()

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	require.NoError(t, err)

	user, err := td.queries.CreateUser(context.Background(), db.CreateUserParams{
		Username:     username,
		PasswordHash: string(hashedPassword),
	})
	require.NoError(t, err)
	return user
}

// CreateTestRole creates a test role
func (td *TestData) CreateTestRole(t *testing.T, name string) db.Role {
	t.Helper()

	role, err := td.queries.CreateRole(context.Background(), name)
	require.NoError(t, err)
	return role
}

// CreateTestMCPTool creates a test MCP tool
func (td *TestData) CreateTestMCPTool(t *testing.T, toolName, description string) db.McpTool {
	t.Helper()
	
	// Create a simple input schema
	inputSchema := []byte(`{
		"type": "object",
		"properties": {
			"name": {
				"type": "string",
				"description": "Name parameter"
			}
		},
		"required": ["name"]
	}`)
	
	tool, err := td.queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    toolName,
		Description: pgtype.Text{String: description, Valid: true},
		InputSchema: inputSchema,
	})
	require.NoError(t, err)
	return tool
}

// CreateTestOpenAPISchema creates a test OpenAPI schema
func (td *TestData) CreateTestOpenAPISchema(t *testing.T, name, filename string) db.OpenapiSchema {
	t.Helper()
	
	schema, err := td.queries.CreateOpenAPISchema(context.Background(), db.CreateOpenAPISchemaParams{
		Name:        name,
		Description: pgtype.Text{String: "Test schema for " + name, Valid: true},
		Filename:    filename,
		Version:     pgtype.Text{String: "1.0.0", Valid: true},
	})
	require.NoError(t, err)
	return schema
}

// AssociateProfileTool associates a tool with a profile
func (td *TestData) AssociateProfileTool(t *testing.T, profileID, toolID int64, acl string) {
	t.Helper()
	
	_, err := td.queries.CreateProfileTool(context.Background(), db.CreateProfileToolParams{
		ProfileID: profileID,
		ToolID:    toolID,
		Acl:       acl,
	})
	require.NoError(t, err)
}

// AssociateUserRole associates a user with a role
func (td *TestData) AssociateUserRole(t *testing.T, userID, roleID int64) {
	t.Helper()
	
	_, err := td.queries.CreateUserRole(context.Background(), db.CreateUserRoleParams{
		UserID: userID,
		RoleID: roleID,
	})
	require.NoError(t, err)
}

// AssociateSchemaConnection associates a schema with a connection
func (td *TestData) AssociateSchemaConnection(t *testing.T, schemaID, connectionID int64, isDefault bool) {
	t.Helper()
	
	_, err := td.queries.CreateSchemaConnection(context.Background(), db.CreateSchemaConnectionParams{
		SchemaID:     schemaID,
		ConnectionID: connectionID,
		IsDefault:    isDefault,
	})
	require.NoError(t, err)
}

// WaitForCondition waits for a condition to be true or times out
func WaitForCondition(t *testing.T, condition func() bool, timeout time.Duration, message string) {
	t.Helper()
	
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		if condition() {
			return
		}
		time.Sleep(10 * time.Millisecond)
	}
	t.Fatalf("Condition not met within timeout: %s", message)
}

// AssertEventuallyTrue asserts that a condition becomes true within a timeout
func AssertEventuallyTrue(t *testing.T, condition func() bool, timeout time.Duration, message string) {
	t.Helper()
	WaitForCondition(t, condition, timeout, message)
}

// CreateTestConnectionAuth creates test connection auth data
func CreateTestConnectionAuth(authType, token, username, password string) util.ConnectionAuth {
	return util.ConnectionAuth{
		Type:     authType,
		Token:    token,
		Username: username,
		Password: password,
	}
}

// CreateBearerAuth creates bearer token auth
func CreateBearerAuth(token string) util.ConnectionAuth {
	return CreateTestConnectionAuth("bearer", token, "", "")
}

// CreateBasicAuth creates basic auth
func CreateBasicAuth(username, password string) util.ConnectionAuth {
	return CreateTestConnectionAuth("basic", "", username, password)
}

// CreateNoAuth creates no auth
func CreateNoAuth() util.ConnectionAuth {
	return CreateTestConnectionAuth("none", "", "", "")
}
