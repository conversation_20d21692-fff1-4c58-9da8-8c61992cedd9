package testutil

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	embeddedpostgres "github.com/fergusstrange/embedded-postgres"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
)

// EmbeddedDB wraps an embedded PostgreSQL instance for testing
type EmbeddedDB struct {
	postgres *embeddedpostgres.EmbeddedPostgres
	config   embeddedpostgres.Config
	connStr  string
	conn     *pgx.Conn
	queries  *db.Queries
}

// findAvailablePort finds an available port for the database
func findAvailablePort() (uint32, error) {
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		return 0, err
	}
	defer listener.Close()

	addr := listener.Addr().(*net.TCPAddr)
	return uint32(addr.Port), nil
}

// NewEmbeddedDB creates a new embedded PostgreSQL instance for testing
func NewEmbeddedDB(t *testing.T) *EmbeddedDB {
	t.Helper()

	// Create a unique database name for this test
	dbName := fmt.Sprintf("testdb_%d", time.Now().UnixNano())

	// Find an available port
	port, err := findAvailablePort()
	require.NoError(t, err, "Failed to find available port")

	// Create config with available port
	config := embeddedpostgres.DefaultConfig().
		Port(port).
		Database(dbName).
		Username("testuser").
		Password("testpass").
		StartTimeout(30*time.Second)

	// Start embedded postgres
	postgres := embeddedpostgres.NewDatabase(config)

	err = postgres.Start()
	require.NoError(t, err, "Failed to start embedded PostgreSQL")

	// Get connection string from config
	connStr := config.GetConnectionURL()

	// Wait a moment for the database to be ready
	time.Sleep(100 * time.Millisecond)

	// Connect to the database
	conn, err := pgx.Connect(context.Background(), connStr)
	require.NoError(t, err, "Failed to connect to embedded PostgreSQL")

	// Test the connection
	err = conn.Ping(context.Background())
	require.NoError(t, err, "Failed to ping embedded PostgreSQL")

	embeddedDB := &EmbeddedDB{
		postgres: postgres,
		config:   config,
		connStr:  connStr,
		conn:     conn,
		queries:  db.New(conn),
	}

	// Run migrations
	embeddedDB.RunMigrations(t)

	return embeddedDB
}

// Queries returns the database queries instance
func (e *EmbeddedDB) Queries() *db.Queries {
	return e.queries
}

// Conn returns the database connection
func (e *EmbeddedDB) Conn() *pgx.Conn {
	return e.conn
}

// ConnectionString returns the database connection string
func (e *EmbeddedDB) ConnectionString() string {
	return e.connStr
}

// Config returns the database configuration
func (e *EmbeddedDB) Config() embeddedpostgres.Config {
	return e.config
}

// Close stops the embedded PostgreSQL instance and cleans up resources
func (e *EmbeddedDB) Close() {
	if e.conn != nil {
		e.conn.Close(context.Background())
	}
	if e.postgres != nil {
		e.postgres.Stop()
	}
}

// CleanupTables truncates all tables for test isolation
func (e *EmbeddedDB) CleanupTables(t *testing.T) {
	t.Helper()
	
	// Truncate all tables in dependency order
	_, err := e.conn.Exec(context.Background(), `
		TRUNCATE TABLE 
			user_roles,
			users,
			roles,
			profile_tools,
			mcp_tool_mappings,
			mcp_tools,
			profiles,
			schema_connections,
			openapi_operations,
			openapi_schemas,
			connections
		RESTART IDENTITY CASCADE;
	`)
	require.NoError(t, err, "Failed to cleanup tables")
}

// RunMigrations runs database migrations using goose
func (e *EmbeddedDB) RunMigrations(t *testing.T) {
	t.Helper()

	// Find the project root (where go.mod is located)
	projectRoot, err := findProjectRoot()
	require.NoError(t, err, "Failed to find project root")

	// Path to migrations directory
	migrationsDir := filepath.Join(projectRoot, "sql", "migrations")

	// Check if migrations directory exists
	if _, err := os.Stat(migrationsDir); os.IsNotExist(err) {
		t.Logf("Migrations directory not found at %s, skipping migrations", migrationsDir)
		return
	}

	// Run goose migrations
	cmd := exec.Command("goose", "-dir", migrationsDir, "postgres", e.connStr, "up")
	cmd.Dir = projectRoot
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Logf("Goose command output: %s", string(output))
		require.NoError(t, err, "Failed to run migrations with goose")
	}
	
	t.Logf("Successfully ran migrations")
}

// findProjectRoot finds the project root directory by looking for go.mod
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			return "", fmt.Errorf("could not find go.mod file")
		}
		dir = parent
	}
}

// SetupDatabase is a convenience function that creates an embedded DB and sets up cleanup
func SetupDatabase(t *testing.T) *EmbeddedDB {
	t.Helper()
	
	embeddedDB := NewEmbeddedDB(t)
	
	// Ensure cleanup happens when test ends
	t.Cleanup(func() {
		embeddedDB.Close()
	})
	
	return embeddedDB
}
