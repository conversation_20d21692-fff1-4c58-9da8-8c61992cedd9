package cli

import (
	"context"
	"io"
	"os"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/tests/testutil"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/urfave/cli/v2"
)

// Helper to create a CLI app for tool testing with embedded database
func createToolTestApp(t *testing.T) (*cli.App, *testutil.EmbeddedDB) {
	t.Helper()

	embeddedDB := testutil.SetupDatabase(t)

	app := &cli.App{
		Commands: []*cli.Command{
			{
				Name: "tool",
				Subcommands: []*cli.Command{
					{
						Name: "associate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile"},
							&cli.StringFlag{Name: "tool"},
							&cli.StringFlag{Name: "acl"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunToolAssociate,
					},
					{
						Name: "disassociate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile"},
							&cli.StringFlag{Name: "tool"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunToolDisassociate,
					},
				},
			},
		},
	}

	return app, embeddedDB
}

func TestRunToolAssociate(t *testing.T) {
	app, embeddedDB := createToolTestApp(t)

	profileName := "test-profile"
	toolName := "test-tool"
	acl := "read"

	// Create test data
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestProfile(t, profileName, "test-path")
	testData.CreateTestMCPTool(t, toolName, "Test tool description")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "tool", "associate", "--profile", profileName, "--tool", toolName, "--acl", acl, "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "associated")
	assert.Contains(t, string(out), toolName)
	assert.Contains(t, string(out), profileName)

	// Verify the association was created in the database
	tools, err := embeddedDB.Queries().ListMCPToolsByProfile(context.Background(), profileName)
	require.NoError(t, err)
	assert.Len(t, tools, 1)
	assert.Equal(t, toolName, tools[0].ToolName)
	assert.Equal(t, acl, tools[0].Acl)
}

func TestRunToolDisassociate(t *testing.T) {
	app, embeddedDB := createToolTestApp(t)

	profileName := "test-profile"
	toolName := "test-tool"

	// Create test data and associate them first
	testData := testutil.NewTestData(embeddedDB.Queries())
	profile := testData.CreateTestProfile(t, profileName, "test-path")
	tool := testData.CreateTestMCPTool(t, toolName, "Test tool description")
	testData.AssociateProfileTool(t, profile.ID, tool.ID, "read")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "tool", "disassociate", "--profile", profileName, "--tool", toolName, "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "disassociated")
	assert.Contains(t, string(out), toolName)
	assert.Contains(t, string(out), profileName)

	// Verify the association was removed from the database
	tools, err := embeddedDB.Queries().ListMCPToolsByProfile(context.Background(), profileName)
	require.NoError(t, err)
	assert.Len(t, tools, 0) // Should be empty after disassociation
}


