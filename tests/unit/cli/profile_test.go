package cli

import (
	"context"
	"io"
	"os"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/tests/testutil"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/urfave/cli/v2"
)

// Helper to create a CLI app and run it with embedded database
func createTestApp(t *testing.T) (*cli.App, *testutil.EmbeddedDB) {
	t.Helper()

	embeddedDB := testutil.SetupDatabase(t)

	app := &cli.App{
		Commands: []*cli.Command{
			{
				Name: "profile",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "description"},
							&cli.StringFlag{Name: "path-segment"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunProfileCreate,
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunProfileList,
					},
					{
						Name: "get",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunProfileGet,
					},
					{
						Name: "update",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "new-name"},
							&cli.StringFlag{Name: "new-description"},
							&cli.StringFlag{Name: "new-path-segment"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunProfileUpdate,
					},
					{
						Name: "delete",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: cmd.RunProfileDelete,
					},
				},
			},
		},
	}

	return app, embeddedDB
}

func TestRunProfileCreate(t *testing.T) {
	app, embeddedDB := createTestApp(t)

	// Capture stdout
	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "profile", "create", "--name", "test-profile", "--description", "A test profile", "--path-segment", "test-path", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout // Restore stdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Profile created successfully")
	assert.Contains(t, string(out), "Name=test-profile")
	assert.Contains(t, string(out), "PathSegment=test-path")

	// Verify the profile was actually created in the database
	profile, err := embeddedDB.Queries().GetProfileByName(context.Background(), "test-profile")
	require.NoError(t, err)
	assert.Equal(t, "test-profile", profile.Name)
	assert.Equal(t, "A test profile", profile.Description.String)
	assert.Equal(t, "test-path", profile.PathSegment)
}

func TestRunProfileList(t *testing.T) {
	app, embeddedDB := createTestApp(t)

	// Create test profiles
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestProfile(t, "profile1", "path1")
	testData.CreateTestProfile(t, "profile2", "path2")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "profile", "list", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Profiles:")
	assert.Contains(t, string(out), "profile1")
	assert.Contains(t, string(out), "profile2")
	assert.Contains(t, string(out), "path1")
	assert.Contains(t, string(out), "path2")
}

func TestRunProfileGet(t *testing.T) {
	app, embeddedDB := createTestApp(t)

	// Create test profile
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestProfile(t, "profile1", "path1")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "profile", "get", "--name", "profile1", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Profile Details:")
	assert.Contains(t, string(out), "Name: profile1")
	assert.Contains(t, string(out), "PathSegment: path1")
}

func TestRunProfileUpdate(t *testing.T) {
	app, embeddedDB := createTestApp(t)

	// Create test profile
	testData := testutil.NewTestData(embeddedDB.Queries())
	profile := testData.CreateTestProfile(t, "old-name", "old-path")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "profile", "update", "--name", "old-name", "--new-name", "new-name", "--new-description", "new-desc", "--new-path-segment", "new-path", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Profile updated successfully")
	assert.Contains(t, string(out), "Name=new-name")
	assert.Contains(t, string(out), "PathSegment=new-path")

	// Verify the profile was actually updated in the database
	updatedProfile, err := embeddedDB.Queries().GetProfileByName(context.Background(), "new-name")
	require.NoError(t, err)
	assert.Equal(t, profile.ID, updatedProfile.ID)
	assert.Equal(t, "new-name", updatedProfile.Name)
	assert.Equal(t, "new-desc", updatedProfile.Description.String)
	assert.Equal(t, "new-path", updatedProfile.PathSegment)
}

func TestRunProfileDelete(t *testing.T) {
	app, embeddedDB := createTestApp(t)

	// Create test profile
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestProfile(t, "profile-to-delete", "path-to-delete")

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	// Run the CLI app with the arguments
	args := []string{"testapp", "profile", "delete", "--name", "profile-to-delete", "--db", embeddedDB.ConnectionString()}
	err := app.Run(args)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	require.NoError(t, err)
	assert.Contains(t, string(out), "Profile profile-to-delete deleted successfully.")

	// Verify the profile was actually deleted from the database
	_, err = embeddedDB.Queries().GetProfileByName(context.Background(), "profile-to-delete")
	assert.Error(t, err) // Should not exist anymore
}
