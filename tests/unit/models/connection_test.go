package models

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
	"github.com/ravan/suse-air/tests/testutil"
)

// Test helper functions
func setupConnectionTest(t *testing.T) (*models.ConnectionService, *testutil.EmbeddedDB) {
	t.Helper()
	embeddedDB := testutil.SetupDatabase(t)
	service := models.NewConnectionService(embeddedDB.Queries())
	return service, embeddedDB
}

func setupSchemaTest(t *testing.T) (*models.SchemaService, *testutil.EmbeddedDB) {
	t.Helper()
	embeddedDB := testutil.SetupDatabase(t)
	service := models.NewSchemaService(embeddedDB.Queries())
	return service, embeddedDB
}

func TestConnectionService_CreateConnection(t *testing.T) {
	service, embeddedDB := setupConnectionTest(t)
	ctx := context.Background()

	// Test data
	req := models.CreateConnectionRequest{
		Name:        "test-conn",
		Description: "Test connection",
		BaseURL:     "https://api.test.com",
		Auth: util.ConnectionAuth{
			Type:  "bearer",
			Token: "test-token",
		},
	}

	// Execute
	result, err := service.CreateConnection(ctx, req)

	// Assert
	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-conn", result.Name)
	assert.Equal(t, "Test connection", result.Description)
	assert.Equal(t, "https://api.test.com", result.BaseURL)
	assert.Equal(t, "bearer", result.Auth.Type)
	assert.Equal(t, "test-token", result.Auth.Token)
	assert.True(t, result.ID > 0, "ID should be assigned")

	// Verify the connection was actually created in the database
	conn, err := embeddedDB.Queries().GetConnectionByName(ctx, "test-conn")
	require.NoError(t, err)
	assert.Equal(t, "test-conn", conn.Name)
	assert.Equal(t, "https://api.test.com", conn.BaseUrl)
	assert.Equal(t, "bearer", conn.AuthType)
}

func TestConnectionService_CreateConnection_InvalidAuthType(t *testing.T) {
	service, _ := setupConnectionTest(t)
	ctx := context.Background()

	req := models.CreateConnectionRequest{
		Name:    "test-conn",
		BaseURL: "https://api.test.com",
		Auth: util.ConnectionAuth{
			Type: "invalid-auth-type",
		},
	}

	// Execute
	result, err := service.CreateConnection(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "invalid auth type")
}

func TestConnectionService_GetConnection(t *testing.T) {
	service, embeddedDB := setupConnectionTest(t)
	ctx := context.Background()

	// First create a connection to retrieve
	testData := testutil.NewTestData(embeddedDB.Queries())
	conn := testData.CreateTestConnection(t, "test-conn", "https://api.test.com")

	// Execute
	result, err := service.GetConnection(ctx, "test-conn")

	// Assert
	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, conn.ID, result.ID)
	assert.Equal(t, "test-conn", result.Name)
	assert.Equal(t, "https://api.test.com", result.BaseURL)
	assert.Equal(t, "bearer", result.Auth.Type)
	assert.Equal(t, "test-token", result.Auth.Token) // From test data factory

}

func TestConnectionService_ListConnections(t *testing.T) {
	service, embeddedDB := setupConnectionTest(t)
	ctx := context.Background()

	// Create test connections
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestConnection(t, "conn1", "https://api1.test.com")

	// Create a basic auth connection manually for more control
	_, err := embeddedDB.Queries().CreateConnection(ctx, db.CreateConnectionParams{
		Name:         "conn2",
		Description:  pgtype.Text{String: "Connection 2", Valid: true},
		BaseUrl:      "https://api2.test.com",
		AuthType:     "basic",
		AuthUsername: pgtype.Text{String: "user", Valid: true},
	})
	require.NoError(t, err)

	// Execute
	result, err := service.ListConnections(ctx)

	// Assert
	require.NoError(t, err)
	assert.Len(t, result, 2)

	// Find connections by name (order might vary)
	var bearerConn, basicConn *models.ConnectionInfo
	for i := range result {
		if result[i].Name == "conn1" {
			bearerConn = &result[i]
		} else if result[i].Name == "conn2" {
			basicConn = &result[i]
		}
	}

	require.NotNil(t, bearerConn)
	require.NotNil(t, basicConn)

	assert.Equal(t, "bearer", bearerConn.Auth.Type)
	assert.Equal(t, "basic", basicConn.Auth.Type)
	assert.Equal(t, "user", basicConn.Auth.Username)
	// Sensitive data should not be included in list view
	assert.Empty(t, bearerConn.Auth.Token)
	assert.Empty(t, basicConn.Auth.Password)

}

func TestConnectionService_DeleteConnection(t *testing.T) {
	service, embeddedDB := setupConnectionTest(t)
	ctx := context.Background()

	// First create a connection to delete
	testData := testutil.NewTestData(embeddedDB.Queries())
	testData.CreateTestConnection(t, "test-conn", "https://api.test.com")

	// Verify it exists
	_, err := embeddedDB.Queries().GetConnectionByName(ctx, "test-conn")
	require.NoError(t, err)

	// Execute delete
	err = service.DeleteConnection(ctx, "test-conn")

	// Assert
	require.NoError(t, err)

	// Verify it's deleted
	_, err = embeddedDB.Queries().GetConnectionByName(ctx, "test-conn")
	assert.Error(t, err) // Should not exist anymore
}

func TestSchemaService_AssociateConnection(t *testing.T) {
	service, embeddedDB := setupSchemaTest(t)
	ctx := context.Background()

	// Create test data
	testData := testutil.NewTestData(embeddedDB.Queries())
	schema := testData.CreateTestOpenAPISchema(t, "test-schema", "test.json")
	conn := testData.CreateTestConnection(t, "test-conn", "https://api.test.com")

	// Execute
	err := service.AssociateConnection(ctx, "test-schema", "test-conn", true)

	// Assert
	require.NoError(t, err)

	// Verify the association was created
	association, err := embeddedDB.Queries().GetSchemaConnection(ctx, db.GetSchemaConnectionParams{
		SchemaID:     schema.ID,
		ConnectionID: conn.ID,
	})
	require.NoError(t, err)
	assert.Equal(t, schema.ID, association.SchemaID)
	assert.Equal(t, conn.ID, association.ConnectionID)
	assert.True(t, association.IsDefault)
}

func TestSchemaService_DisassociateConnection(t *testing.T) {
	service, embeddedDB := setupSchemaTest(t)
	ctx := context.Background()

	// Create test data
	testData := testutil.NewTestData(embeddedDB.Queries())
	schema := testData.CreateTestOpenAPISchema(t, "test-schema", "test.json")
	conn := testData.CreateTestConnection(t, "test-conn", "https://api.test.com")

	// First associate them
	testData.AssociateSchemaConnection(t, schema.ID, conn.ID, false)

	// Verify association exists
	_, err := embeddedDB.Queries().GetSchemaConnection(ctx, db.GetSchemaConnectionParams{
		SchemaID:     schema.ID,
		ConnectionID: conn.ID,
	})
	require.NoError(t, err)

	// Execute disassociation
	err = service.DisassociateConnection(ctx, "test-schema", "test-conn")

	// Assert
	require.NoError(t, err)

	// Verify association is removed
	_, err = embeddedDB.Queries().GetSchemaConnection(ctx, db.GetSchemaConnectionParams{
		SchemaID:     schema.ID,
		ConnectionID: conn.ID,
	})
	assert.Error(t, err) // Should not exist anymore
}
